#!/usr/bin/env python3

import os
import struct

def extract_pngs_from_memory_dump(dump_file, output_dir):
    """Extract PNG files from memory dump"""
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # PNG signature
    png_signature = b'\x89PNG\r\n\x1a\n'
    
    with open(dump_file, 'rb') as f:
        data = f.read()
    
    png_count = 0
    pos = 0
    
    while True:
        # Find next PNG signature
        png_start = data.find(png_signature, pos)
        if png_start == -1:
            break
            
        print(f"Found PNG signature at offset: 0x{png_start:08x}")
        
        # Find the end of this PNG by looking for IEND chunk
        iend_signature = b'IEND\xae\x42\x60\x82'
        iend_pos = data.find(iend_signature, png_start)
        
        if iend_pos == -1:
            print(f"Warning: Could not find IEND for PNG at 0x{png_start:08x}")
            pos = png_start + len(png_signature)
            continue
            
        # PNG ends after the IEND chunk
        png_end = iend_pos + len(iend_signature)
        png_data = data[png_start:png_end]
        
        # Save PNG file
        output_filename = os.path.join(output_dir, f"frame_{png_count:03d}.png")
        with open(output_filename, 'wb') as png_file:
            png_file.write(png_data)
        
        print(f"Extracted PNG {png_count}: {output_filename} (size: {len(png_data)} bytes)")
        
        png_count += 1
        pos = png_end
    
    print(f"Total PNGs extracted: {png_count}")

if __name__ == "__main__":
    extract_pngs_from_memory_dump("memory_dump.bin", "extracted_pngs_new")
