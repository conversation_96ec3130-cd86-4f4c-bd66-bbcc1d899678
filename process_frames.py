#!/usr/bin/env python3

import os
import cv2
import numpy as np
from PIL import Image
import glob

def invert_colors(image):
    """Invert colors of an image"""
    return 255 - image

def enhance_contrast(image):
    """Enhance contrast to reveal hidden details"""
    # Convert to float for processing
    img_float = image.astype(np.float32) / 255.0
    
    # Apply gamma correction to enhance contrast
    gamma = 0.5  # Lower gamma brightens the image
    enhanced = np.power(img_float, gamma)
    
    return (enhanced * 255).astype(np.uint8)

def extract_text_regions(image):
    """Extract potential text regions by thresholding"""
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Apply threshold to get binary image
    _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
    
    return thresh

def process_frame(input_path, output_dir, frame_num):
    """Process a single frame with various transformations"""
    
    # Read the image
    img = cv2.imread(input_path)
    if img is None:
        print(f"Could not read {input_path}")
        return
    
    # Create output directory for this frame
    frame_dir = os.path.join(output_dir, f"frame_{frame_num:03d}")
    os.makedirs(frame_dir, exist_ok=True)
    
    # 1. Original image
    cv2.imwrite(os.path.join(frame_dir, "original.png"), img)
    
    # 2. Inverted colors
    inverted = invert_colors(img)
    cv2.imwrite(os.path.join(frame_dir, "inverted.png"), inverted)
    
    # 3. Enhanced contrast
    enhanced = enhance_contrast(img)
    cv2.imwrite(os.path.join(frame_dir, "enhanced.png"), enhanced)
    
    # 4. Threshold to extract text regions
    thresh = extract_text_regions(img)
    cv2.imwrite(os.path.join(frame_dir, "threshold.png"), thresh)
    
    # 5. Inverted threshold
    thresh_inv = 255 - thresh
    cv2.imwrite(os.path.join(frame_dir, "threshold_inverted.png"), thresh_inv)
    
    # 6. Extract individual color channels
    b, g, r = cv2.split(img)
    cv2.imwrite(os.path.join(frame_dir, "red_channel.png"), r)
    cv2.imwrite(os.path.join(frame_dir, "green_channel.png"), g)
    cv2.imwrite(os.path.join(frame_dir, "blue_channel.png"), b)
    
    # 7. Look for very dark pixels that might contain hidden data
    # Pixels that are almost black but not completely black
    mask = cv2.inRange(img, (1, 1, 1), (10, 10, 10))
    hidden_data = np.zeros_like(img)
    hidden_data[mask > 0] = [255, 255, 255]  # Make visible
    cv2.imwrite(os.path.join(frame_dir, "hidden_data.png"), hidden_data)
    
    print(f"Processed frame {frame_num}")

def extract_text_from_frames(input_dir):
    """Extract any visible text from processed frames"""
    text_data = []
    
    # Look through all processed frames
    for frame_num in range(44):  # We have 44 frames
        frame_dir = os.path.join("processed_frames", f"frame_{frame_num:03d}")
        
        # Check different processed versions for text
        for img_type in ["inverted.png", "threshold_inverted.png", "hidden_data.png"]:
            img_path = os.path.join(frame_dir, img_type)
            if os.path.exists(img_path):
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                
                # Look for white pixels that might be text
                white_pixels = np.sum(img == 255)
                if white_pixels > 100:  # If there are enough white pixels
                    print(f"Frame {frame_num} ({img_type}): {white_pixels} white pixels - potential text")
                    
                    # Try to extract character-like regions
                    contours, _ = cv2.findContours(img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    for contour in contours:
                        x, y, w, h = cv2.boundingRect(contour)
                        # Look for character-sized regions
                        if 5 < w < 50 and 10 < h < 50:
                            char_region = img[y:y+h, x:x+w]
                            # Save character region for manual inspection
                            char_path = os.path.join(frame_dir, f"char_{x}_{y}.png")
                            cv2.imwrite(char_path, char_region)
    
    return text_data

def main():
    input_dir = "extracted_pngs_new"
    output_dir = "processed_frames"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Process all frames
    png_files = sorted(glob.glob(os.path.join(input_dir, "frame_*.png")))
    
    for i, png_file in enumerate(png_files):
        process_frame(png_file, output_dir, i)
    
    print(f"\nProcessed {len(png_files)} frames")
    print("Now extracting potential text regions...")
    
    # Extract text from processed frames
    extract_text_from_frames(input_dir)
    
    print("\nProcessing complete! Check the 'processed_frames' directory.")
    print("Look especially at:")
    print("- inverted.png files for inverted colors")
    print("- threshold_inverted.png for text extraction")
    print("- hidden_data.png for nearly-black pixels made visible")

if __name__ == "__main__":
    main()
